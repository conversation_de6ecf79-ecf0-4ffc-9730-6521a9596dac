jQuery(function($) {
	$('body').on('click', '#nna-sync-objects', function() {
		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncObjects'
			},
			success: function(data) {
				console.log(data);
			}
		});
	});

	$('body').on('click', '#nna-sync-brenollis-objects', function() {
		console.log('Brenollis sync button clicked');
		var $button = $(this);
		$button.prop('disabled', true).text('Syncing...');

		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncBrenollisObjects'
			},
			success: function(data) {
				console.log('Brenollis sync success:', data);
				$button.prop('disabled', false).text('Sünkroniseeri objektid brenollisest');
				if (data.status === 'success') {
					alert('Sync completed successfully!');
				} else {
					alert('Sync completed with issues. Check console for details.');
				}
			},
			error: function(xhr, status, error) {
				console.log('Brenollis sync error:', error);
				console.log('Response:', xhr.responseText);
				$button.prop('disabled', false).text('Sünkroniseeri objektid brenollisest');
				alert('Sync failed: ' + error);
			}
		});
	});

	$('body').on('click', '#nna-sync-users', function() {
		$.ajax({
			url: ajaxurl,
			type: 'post',
			dataType: 'json',
			data: {
				action: 'nnaSyncUsers'
			},
			success: function(data) {
				console.log(data);
			}
		});
	});
});
