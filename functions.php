<?php
if (!session_id()) {
    session_start();
}
if (function_exists ( 'pll_register_string' )) {
	//polylang custom translations
	pll_register_string('General', 'Read more', 'Newtime');
	pll_register_string('General', 'Close', 'Newtime');
	pll_register_string('General', 'Area', 'Newtime');
    pll_register_string('General', 'Phone', 'Newtime');
    pll_register_string('General', 'Mobile', 'Newtime');
    pll_register_string('General', 'Name', 'Newtime');
    pll_register_string('General', 'Email', 'Newtime');
    pll_register_string('General', 'Email Field', 'Newtime');
    pll_register_string('General', 'House', 'Newtime');
    pll_register_string('General', 'Apartment', 'Newtime');
    pll_register_string('General', 'Land', 'Newtime');
    pll_register_string('General', 'Commercial Place', 'Newtime');
    pll_register_string('General', 'Message content', 'Newtime');
    pll_register_string('General', 'Notes', 'Newtime');
    pll_register_string('General', 'Send', 'Newtime');
	pll_register_string('General', 'Take contact', 'Newtime');
	pll_register_string('General', 'No results found!', 'Newtime');
	pll_register_string('General', 'Due date', 'Newtime');
	pll_register_string('General', 'Location', 'Newtime');
	pll_register_string('General', 'Real estate location', 'Newtime');
	pll_register_string('General', 'Back', 'Newtime');
	pll_register_string('General', 'Back to results', 'Newtime');
	pll_register_string('General', 'See location on map', 'Newtime');
	pll_register_string('General', 'Bureau', 'Newtime');
	pll_register_string('General', 'Administrator', 'Newtime');
	pll_register_string('General', 'Evaluators', 'Newtime');
	pll_register_string('General', 'Brokers', 'Newtime');
	pll_register_string('General', 'Pindi real estate development projects', 'Newtime');
	pll_register_string('General', 'See all development projects', 'Newtime');
	pll_register_string('General', '(Currency symbol)', 'Newtime');
	pll_register_string('General', 'Send message', 'Newtime');
	pll_register_string('General', 'Contact us', 'Newtime');
	pll_register_string('General', 'Join', 'Newtime');
	pll_register_string('General', 'Pindi real estate main office', 'Newtime');
	pll_register_string('General', 'Share on facebook', 'Newtime');
	pll_register_string('General', 'Search site...', 'Newtime');
	pll_register_string('General', 'Write to us', 'Newtime');

	pll_register_string('Object', 'Added at', 'Newtime');
	pll_register_string('Object', 'Roof', 'Newtime');
	pll_register_string('Object', 'Gas', 'Newtime');
	pll_register_string('Object', 'Water', 'Newtime');
	pll_register_string('Object', 'Parking', 'Newtime');
	pll_register_string('Object', 'Sewer', 'Newtime');
	pll_register_string('Object', 'Object #ID', 'Newtime');
	pll_register_string('Object', 'Transaction', 'Newtime');
	pll_register_string('Object', 'Condition', 'Newtime');
	pll_register_string('Object', 'Ownership', 'Newtime');
	pll_register_string('Object', 'Year of construction', 'Newtime');
	pll_register_string('Object', 'Rooms (object view)', 'Newtime');
	pll_register_string('Object', 'Rooms (objects view)', 'Newtime');
	pll_register_string('Object', 'Number of rooms', 'Newtime');
	pll_register_string('Object', 'Floor (objects view)', 'Newtime');
	pll_register_string('Object', 'Floor (object view)', 'Newtime');
	pll_register_string('Object', 'Floor (filter)', 'Newtime');
	pll_register_string('Object', 'Total area', 'Newtime');
	pll_register_string('Object', 'Energy mark', 'Newtime');
	pll_register_string('Object', 'Offer price', 'Newtime');
	pll_register_string('Object', 'Additional data', 'Newtime');
	pll_register_string('Object', 'Additional data of property', 'Newtime');
	pll_register_string('Object', 'Ask more info from broker', 'Newtime');
	pll_register_string('Object', 'Total images', 'Newtime');
	pll_register_string('Object', 'Load more objects', 'Newtime');
	pll_register_string('Object', 'Back to top', 'Newtime');
	pll_register_string('Object', 'In Total', 'Newtime');
	pll_register_string('Object', 'Realestate offers', 'Newtime');
	pll_register_string('Object', 'Transaction type', 'Newtime');
	pll_register_string('Object', 'Object type', 'Newtime');
	pll_register_string('Object', 'County', 'Newtime');
	pll_register_string('Object', 'City', 'Newtime');
	pll_register_string('Object', 'Village', 'Newtime');
	pll_register_string('Object', 'Finished by', 'Newtime');
	pll_register_string('Object', 'Find objects', 'Newtime');
	pll_register_string('Object', 'Price from', 'Newtime');
	pll_register_string('Object', 'Price to', 'Newtime');
	pll_register_string('Object', 'From', 'Newtime');
	pll_register_string('Object', 'To', 'Newtime');
	pll_register_string('Object', 'All', 'Newtime');
	pll_register_string('Object', 'Detailed search', 'Newtime');
	pll_register_string('Object', 'Newer first', 'Newtime');
	pll_register_string('Object', 'Newer last', 'Newtime');
	pll_register_string('Object', 'Price ascending', 'Newtime');
	pll_register_string('Object', 'Price descending', 'Newtime');
	pll_register_string('Object', 'Price/m2 ascending', 'Newtime');
	pll_register_string('Object', 'Price/m2 descending', 'Newtime');
	pll_register_string('Object', 'Newest first', 'Newtime');
	pll_register_string('Object', 'Oldest first', 'Newtime');
	pll_register_string('Object', 'Area ascending', 'Newtime');
	pll_register_string('Object', 'Area descending', 'Newtime');
	pll_register_string('Object', 'Size', 'Newtime');
	pll_register_string('Object', 'Size to', 'Newtime');
	pll_register_string('Object', 'Size from', 'Newtime');
	pll_register_string('Object', 'Keyword', 'Newtime');
	pll_register_string('Object', 'Total area (list view)', 'Newtime');
	pll_register_string('Object', 'Rooms (list view)', 'Newtime');
	pll_register_string('Object', 'Floor (list view)', 'Newtime');
	pll_register_string('Object', 'Price per square metre (list view)', 'Newtime');
	pll_register_string('Object', 'Price', 'Newtime');
	pll_register_string('Object', 'Price per square metre (filter)', 'Newtime');
	pll_register_string('Object', 'Search properties (filter)', 'Newtime');
	pll_register_string('Object', 'Favourited', 'Newtime');
	pll_register_string('Object', 'Street, object ID, broker, etc', 'Newtime');
	pll_register_string('Object', 'All object types', 'Newtime');
	pll_register_string('Object', 'Location from map', 'Newtime');
	pll_register_string('Object', 'images total', 'Newtime');
	pll_register_string('Object', 'No objects found', 'Newtime');
	pll_register_string('Object', 'Booked until (objects view)', 'Newtime');
	pll_register_string('Object', 'Booked until (single object)', 'Newtime');
	pll_register_string('Object', 'Send price', 'Newtime');
	pll_register_string('Object', 'Cadastral numbers', 'Newtime');
	pll_register_string('Object', 'Land size', 'Newtime');
	pll_register_string('Object', 'Heating', 'Newtime');
	pll_register_string('Object', 'Building type', 'Newtime');

	pll_register_string('News', 'News', 'Newtime');
	pll_register_string('News', 'No News', 'Newtime');

	pll_register_string('Contact', 'View larger map', 'Newtime');
	pll_register_string('Contact', 'We are located at', 'Newtime');
	pll_register_string('Contact', 'Certificates', 'Newtime');
	pll_register_string('Contact', 'Articles', 'Newtime');
	pll_register_string('Contact', 'Projects', 'Newtime');
	pll_register_string('Contact', 'Success stories', 'Newtime');
	pll_register_string('Contact', 'Awards', 'Newtime');
	pll_register_string('Contact', 'All counties', 'Newtime');

	pll_register_string('404', 'This file or page was not found!', 'Newtime');
	pll_register_string('404', 'Error: 404', 'Newtime');
	pll_register_string('404', 'Please validate correctness', 'Newtime');
	pll_register_string('404', 'Back to home page', 'Newtime');

	pll_register_string('Front page', 'Latest news', 'Newtime');
	pll_register_string('Front page', 'Read all news', 'Newtime');
	pll_register_string('Front page', 'See', 'Newtime');
	pll_register_string('Front page', 'See all offers', 'Newtime');
	pll_register_string('Front page', 'Find yourself a home', 'Newtime');
	pll_register_string('Front page', 'Find commercial area', 'Newtime');
	pll_register_string('Front page', 'See all employers', 'Newtime');
	pll_register_string('Front page', 'Search', 'Newtime');
	pll_register_string('Front page', 'Find our closest Pindi office near you!', 'Newtime');
	pll_register_string('Front page', 'Market analysis', 'Newtime');
	pll_register_string('Front page', 'Front page subscription heading', 'Newtime');
	pll_register_string('Front page', 'Front page subscription intro', 'Newtime');
	pll_register_string('Front page', 'See all new developments', 'Newtime');
	pll_register_string('Front page', 'Realestate analysis and info', 'Newtime');
	pll_register_string('Front page', 'Insert email address', 'Newtime');
	pll_register_string('Front page', 'Successfully joined newsletter', 'Newtime');
	pll_register_string('Front page', 'Newsletter field error', 'Newtime');
	pll_register_string('Front page', 'Newsletter privacy agreement', 'Newtime');
	pll_register_string('Front page', 'Newsletter privacy agreement error', 'Newtime');

	pll_register_string('Sidebar', 'Pindi index', 'Newtime');
	pll_register_string('Sidebar', 'Total offers', 'Newtime');
	pll_register_string('Sidebar', 'New development projects', 'Newtime');

	pll_register_string('Search', 'Search results', 'Newtime');
	pll_register_string('Search', 'Found', 'Newtime');
	pll_register_string('Search', 'results', 'Newtime');
	pll_register_string('Search', 'result', 'Newtime');

	pll_register_string('Menu', 'Menu', 'Newtime');

	pll_register_string('Form', 'Field is required', 'Newtime');
	pll_register_string('Form', 'Has to be email format', 'Newtime');
	pll_register_string('Form', 'Message sent!', 'Newtime');
	pll_register_string('Form', 'Error has occurred! Please try again later.', 'Newtime');

	pll_register_string('Filter', 'All (County)', 'Newtime');
	pll_register_string('Filter', 'All (City)', 'Newtime');
	pll_register_string('Filter', 'All (Village)', 'Newtime');
	pll_register_string('Filter', 'All (Object type)', 'Newtime');
	pll_register_string('Filter', 'All (Transaction type)', 'Newtime');
	pll_register_string('Filter', 'All (Condition)', 'Newtime');

	pll_register_string('Breadcrumbs', 'Home (breadcrumbs)', 'Newtime');
	pll_register_string('Breadcrumbs', 'Search (breadcrumbs)', 'Newtime');

    pll_register_string('Consultation', 'Consultation Email Subject', 'Newtime');
	pll_register_string('Consultation', 'Consultation Form Title', 'Newtime');
	pll_register_string('Consultation', 'Asset Type', 'Newtime');
	pll_register_string('Consultation', 'Asset Address', 'Newtime');

	pll_register_string('Contact form page', 'Olulised lingid', 'Newtime');
	pll_register_string('Contact form page', '24h avariitelefon', 'Newtime');

	pll_register_string('Contact form page', 'Hindamine tellige siit', 'Newtime');
	pll_register_string('Contact form page', 'Kirjutage meile, teeme Teile hea pakkumise!', 'Newtime');
	pll_register_string('Contact form page', 'Sobiva maakleri leidmiseks võtke meiega kohe ühendust!', 'Newtime');
	pll_register_string('Contact form page', 'Võtke ühendust', 'Newtime');
	pll_register_string('Contact form page', 'Küsige pakkumist', 'Newtime');
	pll_register_string('Contact form page', 'Tellige hindamine', 'Newtime');
	pll_register_string('Contact form page', 'Meie hinnakiri', 'Newtime');
	pll_register_string('Contact form page', 'Või helistage meile', 'Newtime');
	pll_register_string('Contact form page', 'Vara liik', 'Newtime');
	pll_register_string('Contact form page', 'Vara aadress', 'Newtime');
	pll_register_string('Contact form page', 'Nimi', 'Newtime');
	pll_register_string('Contact form page', 'E-mail', 'Newtime');
	pll_register_string('Contact form page', 'Telefon', 'Newtime');
	pll_register_string('Contact form page', 'Lisainfo', 'Newtime');
	pll_register_string('Contact form page', 'Korter', 'Newtime');
	pll_register_string('Contact form page', 'Maja', 'Newtime');
	pll_register_string('Contact form page', 'Maatükk', 'Newtime');
	pll_register_string('Contact form page', 'Äripind', 'Newtime');
	pll_register_string('Contact form page', 'Maakond', 'Newtime');
	pll_register_string('Contact form page', 'Harju maakond', 'Newtime');
	pll_register_string('Contact form page', 'Hiiu maakond', 'Newtime');
	pll_register_string('Contact form page', 'Ida-Viru', 'Newtime');
	pll_register_string('Contact form page', 'Jõgeva maakond', 'Newtime');
	pll_register_string('Contact form page', 'Järva maakond', 'Newtime');
	pll_register_string('Contact form page', 'Lääne maakond', 'Newtime');
	pll_register_string('Contact form page', 'Lääne-Viru', 'Newtime');
	pll_register_string('Contact form page', 'Põlva maakond', 'Newtime');
	pll_register_string('Contact form page', 'Pärnu maakond', 'Newtime');
	pll_register_string('Contact form page', 'Rapla maakond', 'Newtime');
	pll_register_string('Contact form page', 'Saare maakond', 'Newtime');
	pll_register_string('Contact form page', 'Tartu maakond', 'Newtime');
	pll_register_string('Contact form page', 'Valga maakond', 'Newtime');
	pll_register_string('Contact form page', 'Viljandi maakond', 'Newtime');
	pll_register_string('Contact form page', 'Võru maakond', 'Newtime');

	pll_register_string('Contact form page', 'Vorm saadeti edukalt välja', 'Newtime');
	pll_register_string('Contact form page', 'Vormi saatmisel tekkisid tehnilised tõrked. Probleemi jätkumisel võtke ühendust saidi halduriga.', 'Newtime');

  pll_register_string('Campaign', 'Campaign newsletter text', 'Newtime');
}

function enqueueScripts() {
	$ver = '2.6';
	wp_enqueue_script('jq', get_template_directory_uri() . '/vendor/jquery/js/jquery-1.11.3.min.js', array(), $ver, false);
	wp_enqueue_script('jq-mob', get_template_directory_uri() . '/vendor/jquery/js/jquery.mobile.custom.min.js', array(), $ver, false);
	wp_enqueue_script('bootstrap', get_template_directory_uri() . '/vendor/bootstrap/js/bootstrap-3.3.5.min.js', array('jq'), $ver, true);
	wp_enqueue_script('dlmenu', get_template_directory_uri() . '/vendor/dlmenu/js/jquery.dlmenu-1.2.1.custom.js', array('jq'), $ver, true);
	wp_enqueue_script('object-fit', get_template_directory_uri() . '/vendor/polyfill-object-fit/js/polyfill.object-fit.core.js', array('jq'), $ver, true);
	wp_enqueue_script('slimscroll', get_template_directory_uri() . '/vendor/slimscroll/js/jquery.slimscroll.min.js', array('jq'), $ver, true);
	wp_enqueue_script('slick', get_template_directory_uri() . '/vendor/slick/js/slick.min.js', array('jq'), $ver, true);
	wp_enqueue_script('imagemapster', get_template_directory_uri() . '/vendor/imagemapster/js/jquery.imagemapster.js', array('jq'), $ver, true);
	wp_enqueue_script('gmap', 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCar7cCzmzycqrt-AUDC59ZPE8XXIZLA7U', array('jq'), $ver, true);
	wp_enqueue_script('emap', get_template_directory_uri() . '/assets/js/estonian-map.js', array('jq', 'imagemapster'), $ver, true);
	wp_enqueue_script('main', get_template_directory_uri() . '/assets/js/main.js', array('jq'), $ver, true);
	wp_enqueue_script('mainhead', get_template_directory_uri() . '/assets/js/main-head.js', array(), $ver, false);
	wp_enqueue_script('tablesaw', get_template_directory_uri() . '/vendor/tablesaw/js/tablesaw.stackonly.js', array('jq'), $ver, true);
	wp_enqueue_script('googleAnalytics', get_template_directory_uri() . '/assets/js/google-analytics.js', array('jq'), $ver, true);
	wp_enqueue_script('form-page', get_template_directory_uri() . '/assets/js/form-page.js', [], $ver, true);

	wp_localize_script('emap', 'wpVariables', localize_vars());
}
add_action( 'wp_enqueue_scripts', 'enqueueScripts' );

if( function_exists('acf_add_options_page') ) {
	acf_add_options_page([
		'page_title' 	=> 'Theme General Settings',
		'menu_title'	=> 'Theme Settings',
		'menu_slug' 	=> 'theme-general-settings',
		'capability'	=> 'edit_posts',
		'redirect'		=> false
	]);
}

function localize_vars() {
    return array(
        'templateDirectory' => get_template_directory_uri(),
        'map_png' => 'map_estonia_hover.png?ver=2',
        'map_svg' => 'map_estonia_hover.svg?ver=2',
    );
}


if ( version_compare( $GLOBALS['wp_version'], '3.6', '<' ) ) {
	require get_template_directory() . '/inc/back-compat.php';
}

if ( ! function_exists( 'newtime_setup' ) ) {
	function newtime_setup() {
		// Add RSS feed links to <head> for posts and comments.
		add_theme_support( 'automatic-feed-links' );

		// Enable support for Post Thumbnails, and declare two sizes.
		add_theme_support( 'post-thumbnails' );
		set_post_thumbnail_size( 672, 372, true );
		add_image_size( 'custom-banner-image', 1170, 'auto', array('center', 'center') );
	}
}
add_action( 'after_setup_theme', 'newtime_setup' );

function newtime_scripts() {
    $ver = '2.5';
	wp_enqueue_style( 'newtime-style', get_stylesheet_uri(), [], $ver);
	//wp_enqueue_script( 'newtime-script', get_template_directory_uri() . '/js/functions.js', array( 'jquery' ), '20140319', true );
}
add_action( 'wp_enqueue_scripts', 'newtime_scripts' );

function remove_editor_menu() {
  remove_action('admin_menu', '_add_themes_utility_last', 101);
}
add_action('_admin_menu', 'remove_editor_menu', 1);


//Register menus
register_nav_menus( array(
	'primary'   => __( 'Primary menu', 'newtime' ),
	'services'   => __( 'Services menu', 'newtime' ),
	'footer'   => __( 'Footer menu', 'newtime' ),
	'mobile'   => __( 'Mobile dropdown', 'newtime' ),
	'mobile-static'   => __( 'Mobile static', 'newtime' ),
) );

//Get id for info page
$infoPageArgs = [
    'post_type' => 'page',
    'fields' => 'ids',
    'nopaging' => true,
    'meta_key' => '_wp_page_template',
    'meta_value' => 'page-templates/info_page.php'
];
$infoPageId = array_values(get_posts($infoPageArgs))[0];

// custom walkers for sidebar menu
class customWalkerSideMenu extends Walker_Nav_Menu {
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"menu-side-submenu\">\n";
	}

	public function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
		$indent = ( $depth ) ? str_repeat( "\t", $depth ) : '';

		$classes = empty( $item->classes ) ? array() : (array) $item->classes;
		$classes[] = 'menu-side-item menu-item-' . $item->ID; /* custom class here */

		$class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
		$class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

		$id = apply_filters( 'nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args, $depth );
		$id = $id ? ' id="' . esc_attr( $id ) . '"' : '';

		$output .= $indent . '<li' . $id . $class_names .'>';

		$atts = array();
		$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
		$atts['target'] = ! empty( $item->target )     ? $item->target     : '';
		$atts['rel']    = ! empty( $item->xfn )        ? $item->xfn        : '';
		$atts['href']   = ! empty( $item->url )        ? $item->url        : '';
		$atts['class']   = 'menu-side-link'; /* custom class here */

		$atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

		$attributes = '';
		foreach ( $atts as $attr => $value ) {
			if ( ! empty( $value ) ) {
				$value = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$item_output = $args->before;
		$item_output .= '<a'. $attributes .'>';
		$item_output .= $args->link_before . apply_filters( 'the_title', $item->title, $item->ID ) . $args->link_after;
		$item_output .= '</a>';
		$item_output .= $args->after;

		$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
	}
}

// custom walkers for header menu
class customWalkerPrimaryMenu extends Walker_Nav_Menu {
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"menu-side-submenu\">\n";
	}

	public function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
		$indent = ( $depth ) ? str_repeat( "\t", $depth ) : '';

		$classes = empty( $item->classes ) ? array() : (array) $item->classes;
		$classes[] = 'menu-item menu-item-' . $item->ID; /* custom class here */

		$class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
		$class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

		$id = apply_filters( 'nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args, $depth );
		$id = $id ? ' id="' . esc_attr( $id ) . '"' : '';

		$output .= $indent . '<li' . $id . $class_names .'>';

		$atts = array();
		$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
		$atts['target'] = ! empty( $item->target )     ? $item->target     : '';
		$atts['rel']    = ! empty( $item->xfn )        ? $item->xfn        : '';
		$atts['href']   = ! empty( $item->url )        ? $item->url        : '';
		$atts['class']   = 'menu-link'; /* custom class here */

		$atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

		$attributes = '';
		foreach ( $atts as $attr => $value ) {
			if ( ! empty( $value ) ) {
				$value = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$item_output = $args->before;
		$item_output .= '<a'. $attributes .'>';
		$item_output .= $args->link_before . apply_filters( 'the_title', $item->title, $item->ID ) . $args->link_after;
		$item_output .= '</a>';
		$item_output .= $args->after;

		$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
	}
}

// custom walkers for front page menu
class customWalkerFrontPageSecondaryMenu extends Walker_Nav_Menu {
	public function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
		$indent = ( $depth ) ? str_repeat( "\t", $depth ) : '';

		$classes = empty( $item->classes ) ? array() : (array) $item->classes;
		$classes[] = 'secondary-menu-item menu-item-' . $item->ID;

		$class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
		$class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

		$id = apply_filters( 'nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args, $depth );
		$id = $id ? ' id="' . esc_attr( $id ) . '"' : '';

		$output .= $indent . '<li' . $id . $class_names .'>';

		$atts = array();
		$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
		$atts['target'] = ! empty( $item->target )     ? $item->target     : '';
		$atts['rel']    = ! empty( $item->xfn )        ? $item->xfn        : '';
		$atts['href']   = ! empty( $item->url )        ? $item->url        : '';
		$atts['class']  = 'secondary-menu-link';

		$atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

		$attributes = '';
		foreach ( $atts as $attr => $value ) {
			if ( ! empty( $value ) ) {
				$value = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$item_output = $args->before;
		$item_output .= '<a'. $attributes .'>';
		/** This filter is documented in wp-includes/post-template.php */
		$item_output .= $args->link_before . apply_filters( 'the_title', $item->title, $item->ID ) . $args->link_after;
		$item_output .= '</a>';
		$item_output .= $args->after;

		$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );
	}

	public function end_el( &$output, $item, $depth = 0, $args = array() ) {
		$output .= "</li>\n";
	}
}

// custom walkers for footer menu
class footerMenuWalker extends Walker_Nav_Menu {
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		if($depth == 0){
			$output .= "\n$indent<ul class=\"menu-footer-list\">\n";
		}
	}

	public function end_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		if($depth == 0){
			$output .= "$indent</ul>\n";
		}
	}

	public function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
		$indent = ( $depth ) ? str_repeat( "\t", $depth ) : '';
		$classes[] = 'menu-footer-item';

		$class_names = join( ' ', apply_filters( 'nav_menu_css_class', array_filter( $classes ), $item, $args, $depth ) );
		$class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';


		if ($depth == 0) {
			$output .= $indent . '<div class="col-sm-y menu-footer">';
			$output .= $indent . '<div class="menu-footer-heading">';
		} else{
			$output .= $indent . '<li' . $class_names .'>';
		}

		$atts = array();
		$atts['title']  = ! empty( $item->attr_title ) ? $item->attr_title : '';
		$atts['target'] = ! empty( $item->target )     ? $item->target     : '';
		$atts['rel']    = ! empty( $item->xfn )        ? $item->xfn        : '';
		$atts['href']   = ! empty( $item->url )        ? $item->url        : '';
		$atts['class']  = 'menu-footer-link';

		$atts = apply_filters( 'nav_menu_link_attributes', $atts, $item, $args, $depth );

		$attributes = '';
		foreach ( $atts as $attr => $value ) {
			if ( ! empty( $value ) ) {
				$value = ( 'href' === $attr ) ? esc_url( $value ) : esc_attr( $value );
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$item_output = $args->before;
		if ($depth == 0) {
			$item_output .= '<h4>';
		} else{
			$item_output .= '<a'. $attributes .'>';
		}
		/** This filter is documented in wp-includes/post-template.php */
		$item_output .= $args->link_before . apply_filters( 'the_title', $item->title, $item->ID ) . $args->link_after;
		if ($depth == 0) {
			$item_output .= '</h4>';
		}else{
			$item_output .= '</a>';
		}
		$item_output .= $args->after;

		$output .= apply_filters( 'walker_nav_menu_start_el', $item_output, $item, $depth, $args );

		if ($depth == 0) {
			$output .= '</div>';
		}
	}

	public function end_el( &$output, $item, $depth = 0, $args = array() ) {
		if ($depth == 0) {
			$output .= "</div>\n";
		}
	}
}

class customWalkerMobMain extends Walker_Nav_Menu {
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"dl-submenu\">\n";
	}
}

class customWalkerMobFooter extends Walker_Nav_Menu {
	public function start_lvl( &$output, $depth = 0, $args = array() ) {
		$indent = str_repeat("\t", $depth);
		$output .= "\n$indent<ul class=\"dl-submenu\">\n";
	}
}

// Brokers
function registerBrokersTaxonomies() {
	register_taxonomy(
		'active_location',
		'broker',
		array(
			'label' => __( 'Active location' ),
			'rewrite' => array( 'slug' => 'active-location' ),
			'hierarchical' => true,
		)
	);
	register_taxonomy(
		'team-group',
		'broker',
		array(
			'label' => __( 'Group/Team' ),
			'rewrite' => array( 'slug' => 'team-group' ),
			'hierarchical' => true,
		)
	);
	register_taxonomy(
		'local-group',
		'broker',
		array(
			'label' => __( 'Local Group' ),
			'rewrite' => array( 'slug' => 'local-group' ),
			'hierarchical' => true,
		)
	);
	register_taxonomy(
		'display-order',
		'broker',
		array(
			'label' => __( 'Display order' ),
			'rewrite' => array( 'slug' => 'display-order' ),
			'hierarchical' => true,
		)
	);
}
add_action( 'init', 'registerBrokersTaxonomies' );

function addLocationTranslation() {
	$taxonomy = 'active_location';
	$terms = get_terms($taxonomy);

    foreach ($terms as $singleTerm) {
        $termName = $singleTerm->name . ' (filter office name)';
        pll_register_string('Contact group', $termName, 'Newtime');
    }
}
add_action('init', 'addLocationTranslation');

// job offers
function createJobOfferPosttype() {
  register_post_type(
  	'job_offers',
    array(
      'labels' => array(
        'name' => __( 'Job Offers' ),
        'singular_name' => __( 'Job Offer' )
      ),
      'public' => true,
      'rewrite' => array('slug' => 'vahendamine'),
    )
  );
}
add_action( 'init', 'createJobOfferPosttype' );

function registerJobOfferTaxonomies(){
	register_taxonomy(
		'job_location',
		'job_offers',
		array(
			'label' => __( 'Job Location' ),
			'rewrite' => array( 'slug' => 'job-location' ),
			'hierarchical' => true,
		)
	);
}
add_action('init', 'registerJobOfferTaxonomies');

// new developments
function createNewDevelopmentsPosttype() {
  register_post_type(
  	'new-developments',
    array(
      'labels' => array(
        'name' => __( 'New Developments' ),
        'singular_name' => __( 'New Development' )
      ),
      'public' => true,
      'rewrite' => array('slug' => 'new-developments'),
    )
  );
}
add_action( 'init', 'createNewDevelopmentsPosttype' );

function registerNewDevelopmentsTaxonomies(){
	register_taxonomy(
		'new-developments-property-type',
		'new-developments',
		array(
			'label' => __( 'Property Type' ),
			'rewrite' => array( 'slug' => 'property-type' ),
			'hierarchical' => true,
		)
	);
}
add_action('init', 'registerNewDevelopmentsTaxonomies');

// Market Surveys
function createMarketSurveysPosttype() {
	register_post_type(
	  	'market-surveys',
	    array(
			'labels' => array(
				'name' => __( 'Market Surveys' ),
				'singular_name' => __( 'Market Survey' )
			),
			'public' => true,
			'rewrite' => array(
				'slug' => 'market-surveys',
			),
			'supports' => array(
				'title',
				'editor',
				'thumbnail',
			),
			'has_archive' => true,
		)
	);
}
add_action( 'init', 'createMarketSurveysPosttype' );

// filter to acf relations query
function my_relationship_query($args, $field, $post) {
    // add language filter
    $curLang = pll_get_post_language($post->ID);
    $args['lang'] = $curLang;
    return $args;
}
add_filter('acf/fields/relationship/query', 'my_relationship_query', 10, 3);

// get random contact circle
function getContact($customArgs = array()){
	$args = array(
		'contactsType' => '',
		'title' => '',
		'exludeRandom' => false,
		'pageID' => false,
		'customFieldsName' => '',
	);
	$args = array_merge($args, $customArgs);

	$contact = [];
	$singleContact = "";

	// get id
	global $infoPageId;

	// get contacts per type
	switch ($args['contactsType']) {
		case 'fromInfoPage':
			$contacts = get_field($args['customFieldsName'], pll_get_post($infoPageId));
			break;
		case 'postID':
			$singleContact = get_post($args['pageID']);
			break;
		case 'field':
			if ($args['pageID']) {
				$contacts = get_field($args['customFieldsName'], $args['pageID']);
			} else{
				$contacts = get_field($args['customFieldsName']);
			}
			break;
		default:
			$contacts = get_fields();
			break;
	}

	// pick a random contact
	if (isset($contacts) && !empty($contacts)){
		$count = count($contacts);
		if ($args['exludeRandom'] && $count > 1) {
			$rand = rand(0, $count - 1);
			while ($args['exludeRandom'] == $contacts[$rand]->ID) { // make sure their id is different
				$rand = rand(0, $count - 1);
			}
		} else{
			$rand = rand(0, $count - 1);
		}
		$singleContact = $contacts[$rand];
	}

	// add fields from acf
	if (!isset($singleContact) || empty($singleContact)) {
		return;
	}
	$contact['contactID'] = $singleContact->ID;
	$contact['link'] = get_the_permalink($singleContact->ID);
	$contact['name'] = $singleContact->post_title;
	$contact['title'] = $args['title'];
	$contact['occupation'] = get_field('occupation', $singleContact->ID);
	$contact['phone'] = get_field('phone_number', $singleContact->ID);
	$contact['mobile'] = get_field('mobile_number', $singleContact->ID);
	$contact['email'] = get_field('email', $singleContact->ID);
	$contact['thumbnail'] = get_field('contact_thumbnail_image', $singleContact->ID);
	$contact['languages'] = get_field('languages', $singleContact->ID);
	$contact['oldRand'] = $singleContact->ID;

	return $contact;
}
function renderContact($contact, $customArgs = array()){
	$args = array(
		'takeContact' => true,
		'gaCategory' => 'Other',
		'thumbnailClass' => '',
		'infoClass' => '',
		'buttonsClass' => '',
		'singleButtonClass' => '',
	);
	$args = array_merge($args, $customArgs);
	if (!$contact) {
		return;
	}
	?>
	<div class="contact-wrap">
		<div class="contact">
			<?php
				if ($contact['title']) {
					echo '<div class="contact-heading">';
					echo '<h2>' . $contact['title'] . '</h2>';
					echo '</div>';
				}
			?>
			<div class="contact-thumbnail <?php echo $args['thumbnailClass']; ?>">
				<?php if ($contact['thumbnail']): ?>
					<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
				<?php else: ?>
					<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
				<?php endif; ?>
			</div>
			<div class="contact-info <?php echo $args['infoClass']; ?>">
				<?php if ($contact['name']): ?>
					<div class="contact-info-name"><a class="contact-info-link" href="<?php echo $contact['link']; ?>"><?php echo $contact['name']; ?></a></div>
				<?php endif ?>
				<?php if ($contact['occupation']): ?>
					<div class="contact-info-title"><?php echo $contact['occupation']; ?></div>
				<?php endif ?>
				<?php if ($contact['languages']): ?>
					<div class="contact-languages">
						<div class="flags">
							<ul class="flags-list">
								<?php
									foreach ($contact['languages'] as $lang) {
										echo '<li class="flag-item flag-' . $lang . '"></li>';
									}
								?>
							</ul>
						</div>
					</div>
				<?php endif ?>
				<ul class="contact-details">
					<?php if ($contact['phone']): ?>
						<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>"><!-- <?php pll_e('Phone'); ?>: --><?php echo $contact['phone'] ?></a></li>
					<?php endif; ?>
					<?php if ($contact['mobile']): ?>
						<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>"><!-- <?php pll_e('Mobile'); ?>: --><?php echo $contact['mobile'] ?></a></li>
					<?php endif; ?>
					<?php if ($contact['email']): ?>
						<li class="contact-details-item"><!-- <?php pll_e('Email'); ?>: --><a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>"><?php echo $contact['email'] ?></a></li>
					<?php endif; ?>
				</ul>
				<div class="js-contact-links contact-links">
					<div class="make-contact js-make-contact">
						<div class="make-contact-lightbox js-close-make-contact">
						</div>
						<div class="make-contact-box mailto">
							<div class="mailto-heading">
								<div class="gallery-close-button mailto-close-button js-close-make-contact"></div>
								<h4 class="mailto-heading-header"><?php pll_e('Contact us') ?></h4>
							</div>
							<div class="mailto-content clearfix">
								<div class="mailto-content-contact">
									<div class="contact">
										<div class="contact-thumbnail contact-thumbnail-block mailto-contact-thumbnail">
											<?php if ($contact['thumbnail']): ?>
												<img src="<?php echo $contact['thumbnail']['sizes']['medium']; ?>" alt="<?php echo $contact['thumbnail']['alt']; ?>" class="contact-thumbnail-img">
											<?php else: ?>
												<img src="<?php echo get_template_directory_uri(); ?>/assets/img/no_pic_placeholder.png" alt="No image available" class="contact-thumbnail-img">
											<?php endif; ?>
										</div>
										<div class="contact-info contact-info-block">
											<?php
												if ($contact['name']) {
													echo '<div class="contact-info-name">' . $contact['name'] . '</div>';
												}

												if ($contact['occupation']){
													echo '<div class="contact-info-title">' . $contact['occupation'] . '</div>';
												}

												if ($contact['languages']){
													echo '<div class="contact-languages">';
													echo '<div class="flags">';
													echo '<ul class="flags-list">';
													foreach ($contact['languages'] as $lang) {
														echo '<li class="flag-item flag-' . $lang . '"></li>';
													}
													echo '</ul>';
													echo '</div>';
													echo '</div>';
												}
											?>
											<ul class="contact-details">
												<?php if ($contact['phone']): ?>
													<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['phone']; ?>"> <!-- <?php pll_e('Phone'); ?>: --><?php echo $contact['phone'] ?><a></li>
												<?php endif; ?>
												<?php if ($contact['mobile']): ?>
													<li class="contact-details-item"><a class="contact-details-item-link" href="tel:<?php echo $contact['mobile']; ?>"><!-- <?php pll_e('Mobile'); ?>: --><?php echo $contact['mobile'] ?></a></li>
												<?php endif; ?>
												<?php if ($contact['email']): ?>
													<li class="contact-details-item"><!-- <?php pll_e('Email'); ?>: --><a class="contact-details-item-link" href="mailto:<?php echo $contact['email']; ?>"><?php echo $contact['email'] ?></a></li>
												<?php endif; ?>
											</ul>
										</div>
									</div>
								</div>

								<?php
									$gaIdentifier = '';
									$curUrl = get_the_permalink();
									$siteUrl = site_url();
									$gaIdentifier = str_replace($siteUrl, '', $curUrl);
								?>
								<form class="ga-contact-form js-contact-person-form mailto-content-form" identifier-action="sendMailTakeContact" ga-identifier="<?php echo $gaIdentifier; ?>" ga-category="<?php echo $args['gaCategory']; ?>">
									<div class="js-form-submitted-message mailto-form-after-submit-message">
									</div>
									<div class="mailto-form-group js-field">
										<input type="hidden" name="mailForm[url]" value="<?php echo $curUrl;?>">
										<input name="mailForm[id]" type="text" value="<?php echo $contact['contactID']; ?>" hidden>
										<label class="mailto-label">
											<?php pll_e('Name'); ?>:
										</label>
										<input name="mailForm[name]" type="text" class="js-input js-required mailto-form-input">
										<span class='js-error'></span>
									</div>
									<div class="mailto-form-group mailto-form-group-last js-field">
										<label class="mailto-label">
											<?php pll_e('Email'); ?>:
										</label>
										<input name="mailForm[email]" type="text" class="js-input js-required js-email mailto-form-input">
										<span class='js-error'></span>
									</div>
									<div class="clearfix"></div>

									<div class="mailto-form-group-full js-field">
										<label class="mailto-label">
											<?php pll_e('Message content'); ?>:
										</label>
										<textarea name="mailForm[message]" class="js-input js-required mailto-form-textarea"></textarea>
										<span class='js-error'></span>
									</div>
									<button class="js-contact-person-form-submit btn main-button mailto-button" type="button"><?php pll_e('Send message'); ?></button>
								</form>
							</div>
						</div>
					</div>

					<?php if ($args['takeContact']) { ?>
						<button class="btn main-button contact-links-button js-make-contact-button <?php echo $args['buttonsClass']; ?>">
							<?php pll_e('Take contact') ?>
						</button>
					<?php } ?>

					<a class="link-button contact-links-link <?php echo $args['buttonsClass']; ?> <?php echo $args['singleButtonClass']; ?>" href="<?php //echo get_permalink($officeContact['id']); ?>">
						<?php pll_e('See all employers'); ?>
					</a>
				</div>
			</div>
		</div>
	</div>
	<?php
}

//get random object or new-devepment post
function getProject($customArgs = array()){
	$args = array(
		'items' => array(),
		'limit' => 1,
	);
	$args = array_merge($args, $customArgs);
	if (!$args['items']) return;

	$objects = array();
	if (count($args['items']) > $args['limit']) {
		$itemsKeys = array_rand($args['items'], $args['limit']);
	} else{
		$itemsKeys = array_keys($args['items']);
	}
	foreach ($itemsKeys as $key) {
		$item = $args['items'][$key];
		if ($item->post_type == 'object') {
			$condition = array('PostIds.' . pll_current_language() => $item->ID);
			$data = NnaApi::getInstance()->getObjects($condition);
			$objectData = $data[0]->Object;

			$object = array(
				'ID' => $item->ID,
				'name' => get_the_title($item->ID),
				'images' => get_field('images', $item->ID),
				'transactionType' => NnaTranslations::getInstance()->__($objectData->transactions->transaction->type) . ', ' . NnaTranslations::getInstance()->__($objectData->objectTypes->type),
				'url' => get_the_permalink($item->ID),
		 	);
			array_push($objects, $object);
		} elseif ($item->post_type == 'new-developments'){
			$object = array(
				'ID' => $item->ID,
				'name' => get_the_title($item->ID),
				'images' => get_field('product_images', $item->ID),
				'transactionType' => get_field('transaction_type', $item->ID),
				'url' => get_the_permalink($item->ID),
		 	);
			array_push($objects, $object);
		}
	}
	return $objects;
}

// pll get translated page id
function getTranslatedId($postID, $model_type = 'term'){
	global $polylang;
	$postIDs = $polylang->model->$model_type->get_translations($postID);
	$cur_lang = pll_current_language();

	foreach ($postIDs as $lang => $id) {
		if ($cur_lang == $lang) {
			return $id;
		}
	}

	return $postID;
}

// get excerpt by id, remove html tags
function getContentByIdNoTags($post_id) {
	global $post;
	$save_post = $post;
	$post = get_post($post_id);
	setup_postdata($post);
	$output = strip_tags(get_the_content());
	$post = $save_post;
	setup_postdata($post);
	return $output;
}


function customExcerpt($charlength) {
	$excerpt = get_the_excerpt();
	$charlength++;

	if ( mb_strlen( $excerpt ) > $charlength ) {
		$subex = mb_substr( $excerpt, 0, $charlength - 5 );
		$exwords = explode( ' ', $subex );
		$excut = - ( mb_strlen( $exwords[ count( $exwords ) - 1 ] ) );
		if ( $excut < 0 ) {
			echo mb_substr( $subex, 0, $excut );
		} else {
			echo $subex;
		}
		echo '...';
	} else {
		echo $excerpt;
	}
}

// sort item by $post->displayOrder where lower comes first
function sortByVal($a, $b){
	$aOrder = preg_replace("/[^0-9]/", "", $a->displayOrder);
	$bOrder = preg_replace("/[^0-9]/", "", $b->displayOrder);
	if ((int)$aOrder == (int)$bOrder) {
		return strcmp($a->post_title, $b->post_title);
	}
	return ((int)$aOrder < (int)$bOrder) ? -1 : 1;
}

// set new post per page
function postsperpage($limits) {
	if (is_search()) {
		global $wp_query;
		$wp_query->query_vars['posts_per_page'] = 100;
	}
	return $limits;
}
add_filter('post_limits', 'postsperpage');

// custom RSS
function myfeed_request($qv) {
	if (isset($qv['feed']) && !isset($qv['post_type']))
		$qv['post_type'] = array(
			'post',
			'market-surveys',
		);
	return $qv;
}
add_filter('request', 'myfeed_request');

// ajax query
function title_filter($where, $wp_query){
    global $wpdb;

    if($search_term = $wp_query->get( 'post_title_like' )){
        /*using the esc_like() in here instead of other esc_sql()*/
        $search_term = $wpdb->esc_like($search_term);
        $search_term = ' \'%' . $search_term . '%\'';
        $where .= ' AND ' . $wpdb->posts . '.post_title LIKE '.$search_term;
    }

    return $where;
}
add_filter('posts_where', 'title_filter', 10, 2);

// remove 0-s that are after .
function trimTrailingZeroes($nbr) {
    return strpos($nbr,'.')!==false ? rtrim(rtrim($nbr,'0'),'.') : $nbr;
}


// delete images on post delete
function delete_post_media( $post_id ) {

    $attachments = get_posts( array(
        'post_type'      => 'attachment',
        'posts_per_page' => -1,
        'post_status'    => 'any',
        'post_parent'    => $post_id
    ) );

    foreach ( $attachments as $attachment ) {
        if ( false === wp_delete_attachment( $attachment->ID ) ) {
            // Log failure to delete attachment.
        }
    }
}
add_action('before_delete_post', 'delete_post_media');


add_filter('pll_translated_post_type_rewrite_slugs', function($post_type_translated_slugs) {
    $slugChangePostTypes = array(
        'market-surveys',
        'object',
        'broker',
        'job_offers',
        'new-developments',
    );
    // register translatable slug
    foreach ($slugChangePostTypes as $slug) {
        pll_register_string('Slug', $slug . '-slug', 'Newtime');
    }

    // set post types to translate
    $post_type_translated_slugs = array(
        'market-surveys' => array(
            'et' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'turuulevaated',
                ),
            ),
            'en' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'market_surveys',
                ),
            ),
            'ru' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'обзор_рынка',
                ),
            ),
        ),
        'object' => array(
            'et' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'kinnisvarapakkumised',
                ),
            ),
            'en' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'kinnisvarapakkumised',
                ),
            ),
            'ru' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'kinnisvarapakkumised',
                ),
            ),
        ),
        'broker' => array(
            'et' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'meeskond',
                ),
            ),
            'en' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'team',
                ),
            ),
            'ru' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'команда',
                ),
            ),
        ),
        'job_offers' => array(
            'et' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'toopakkumised',
                ),
            ),
            'en' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'job_offers',
                ),
            ),
            'ru' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'вакансии',
                ),
            ),
        ),
        'new-developments' => array(
            'et' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'arendusprojektid',
                ),
            ),
            'en' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'development-projects',
                ),
            ),
            'ru' => array(
                'has_archive' => false,
                'rewrite' => array(
                    'slug' => 'новостройки',
                ),
            ),
        ),

    );

    return $post_type_translated_slugs;
});

// get current item parent
function get_menu_parent( $menu, $post_id = null ) {

    $post_id        = $post_id ? : get_the_ID();
    $menu_items     = wp_get_nav_menu_items( $menu );
    $parent_item_id = wp_filter_object_list( $menu_items, array( 'object_id' => $post_id ), 'and' );

    if ( ! empty( $parent_item_id ) ) {
        $parent_item_id = array_shift( $parent_item_id );
        $parent_post_id = wp_filter_object_list( $menu_items, array( 'ID' => $parent_item_id->menu_item_parent ), 'and' );
        if ( ! empty( $parent_post_id ) ) {
            $parent_post_id = array_shift( $parent_post_id );
            // print_r($parent_post_id);

            return get_post( $parent_post_id );
        }
    }

    return false;
}

function theBreadCrumbs() {
	$showOnHome = 0; // 1 - show breadcrumbs on the homepage, 0 - don't show
	$delimiter = ''; // delimiter between crumbs
	$home = pll__('Home (breadcrumbs)'); // text for the 'Home' link
	$showCurrent = 1; // 1 - show current post/page title in breadcrumbs, 0 - don't show
	$before = '<li class="breadcrumbs-item"><a href="#" class="breadcrumbs-link breadcrumbs-link-current">'; // tag before the current crumb
	$after = '</a></li>'; // tag after the current crumb

	global $post;
	$homeLink = get_bloginfo('url');

	echo '<div class="breadcrumbs hidden-print hidden-xs"><ul class="breadcrumbs-list"><li class="breadcrumbs-item"><a class="breadcrumbs-link" href="' . $homeLink . '">' . $home . '</a></li> ' . $delimiter . ' ';

	if ( is_search() ) {
		echo $before . pll__('Home (breadcrumbs)') . $after;

	} elseif ( is_single() && !is_attachment() ) {
		if ( get_post_type() != 'post' ) {
			$template = '';
			$templates = [
				'market-surveys' => 'page-templates/market_surveys.php',
				'object' => 'nna-objects.php',
				'job_offers' => 'page-templates/job_offers.php',
				'new-developments' => 'page-templates/new_developments.php',
				'new-developments' => 'page-templates/inquiry-sent-page.php',
			];
 
			$post_type = get_post_type();
			if (isset($templates[$post_type])) {
				$template = $templates[$post_type];
			}
			if ($template) {
				$postTypePageArgs = [
				    'post_type' => 'page',
				    'fields' => 'ids',
				    'nopaging' => true,
				    'meta_key' => '_wp_page_template',
				    'meta_value' => $template,
				];
				$postTypePageId = array_values(get_posts($postTypePageArgs))[0];
				$link = get_the_permalink($postTypePageId);
				$title = get_the_title($postTypePageId);

				echo '<li class="breadcrumbs-item"><a class="breadcrumbs-link" href="' . $link . '/">' . $title . '</a></li>';
			}

			if ($showCurrent == 1) echo ' ' . $delimiter . ' ' . $before . get_the_title() . $after;
		} else {
			// $cat = get_the_category(); $cat = $cat[0];
			// $cats = get_category_parents($cat, TRUE, ' ' . $delimiter . ' ');
			// if ($showCurrent == 0) $cats = preg_replace("#^(.+)\s$delimiter\s$#", "$1", $cats);
			// echo $cats;
			if ($showCurrent == 1) echo $before . get_the_title() . $after;
		}

	} elseif ( !is_single() && !is_page() && get_post_type() != 'post' && !is_404() ) {
		$post_type = get_post_type_object(get_post_type());
		echo $before . $post_type->labels->singular_name . $after;

	} elseif (is_page()) {
		$locations = get_registered_nav_menus();
		$menus = wp_get_nav_menus();
		$menu_locations = get_nav_menu_locations();

		$location_id = 'primary';
		$menuName = '';
		if (isset($menu_locations[ $location_id ])) {
			foreach ($menus as $menu) {
				// If the ID of this menu is the ID associated with the location we're searching for
				if ($menu->term_id == $menu_locations[ $location_id ]) {
					$menuName = $menu->name;
					break;
				}
			}
		}
		$postId = $post->ID;
		$parentItems = array();
		while ($ret = get_menu_parent($menuName, $postId)) {
			$postId = $ret->object_id;
			$postItem = array(
				'ID' => $ret->object_id,
				'name' => $ret->title,
			);
			array_push($parentItems, $postItem);
		}

		if ($parentItems) {
			$parentItems = array_reverse($parentItems);
			foreach ($parentItems as $singleParentItem) {
				echo '<li class="breadcrumbs-item"><a class="breadcrumbs-link" href="' . get_permalink($singleParentItem['ID']) . '">' . $singleParentItem['name'] . '</a></li>';
			}
		}
		if ($showCurrent == 1) echo ' ' . $delimiter . ' ' . $before . get_the_title() . $after;

	} elseif ( is_404() ) {
		echo $before . 'Error 404' . $after;
	}

	echo '</ul></div>';
}


function insertFbInHead() {
	global $post;

	// Get the current post object, fallback to queried object if $post is null
	$current_post = $post;
	if (!$current_post) {
		$current_post = get_queried_object();
	}

	// If we still don't have a post object, return early
	if (!$current_post || !isset($current_post->ID) || !isset($current_post->post_type)) {
		return;
	}

	if ($current_post->post_type == 'object' || $current_post->post_type == 'new-developments'){
		if ($current_post->post_type == 'object') {
			$imageFieldName = 'images';
		} elseif ($current_post->post_type == 'new-developments') {
			$imageFieldName = 'product_images';
		}
		$images = get_field($imageFieldName, $current_post->ID);
		if (isset($images) && !empty($images)) {
			echo '<meta property="og:image" content="' . $images[0]['image']['sizes']['custom-banner-image'] . '"/>';
		}
	} else{
		$thumbnail_src = wp_get_attachment_image_src( get_post_thumbnail_id( $current_post->ID ), 'large' );
		if ($thumbnail_src && isset($thumbnail_src[0])) {
			echo '<meta property="og:image" content="' . esc_attr( $thumbnail_src[0] ) . '"/>';
		}
	}
	echo "
";
}
add_action( 'wp_head', 'insertFbInHead', 5 );

function getObjectDetails($object) {
	$details = [];

	$date = explode('-', explode(' ', $object->lastModified)[0]);
	$details[] = [
		'label' => 'Added at',
		'data' => substr($date[2], 0, 2) . ' ' . $date[1] . ' ' . $date[0],
	];

	if ($object->originalId) {
		$details[] = [
			'label' => 'Object #ID',
			'data' => $object->originalId,
		];
	}

	if ($object->transactions->transaction->type) {
		$details[] = [
			'label' => 'Transaction',
			'data' => strtolower(NnaTranslations::getInstance()->__($object->transactions->transaction->type) . ', ' . NnaTranslations::getInstance()->__($object->objectTypes->type)),
		];
	}

	if ($object->alacrity) {
		$items = explode(',', $object->alacrity);
		foreach ($items as &$item) {
			$item = strtolower(NnaTranslations::getInstance()->__($item));
		}

		$details[] = [
			'label' => 'Condition',
			'data' => implode(', ', $items),
		];
	}

	if ($object->propertyType) {
		$details[] = [
			'label' => 'Ownership',
			'data' => strtolower(NnaTranslations::getInstance()->__($object->propertyType)),
		];
	}

	if (isset($object->cadastralNumbers) && !empty($object->cadastralNumbers->cadastralNumber)) {
		$details[] = [
			'label' => 'Cadastral numbers',
			'data' => $object->cadastralNumbers->cadastralNumber,
		];
	}

	if ($object->buildingStructure) {
		$details[] = [
			'label' => 'Building type',
			'data' => strtolower(getCommaSeparatedTranslationsFromString($object->buildingStructure)),
		];
	}

	if ($object->buildYear && $object->buildYear != 0000) {
		$details[] = [
			'label' => 'Year of construction',
			'data' => $object->buildYear,
		];
	}

	if ($object->numberOfRooms) {
		$details[] = [
			'label' => 'Rooms (object view)',
			'data' => $object->numberOfRooms,
		];
	}

	if ($object->floorNumber) {
		$details[] = [
			'label' => 'Floor (object view)',
			'data' => $object->floorNumber.'/'.$object->numberOfFloors,
		];
	}

	if ($object->areaSize && !$object->areaSize->_ == 0) {
		$details[] = [
			'label' => 'Total area',
			'data' => trimTrailingZeroes(number_format((float)$object->areaSize->_, 2, '.', ' ')) . ' m²',
		];
	}

	if ($object->landSize && !$object->landSize->_ == 0) {
		$details[] = [
			'label' => 'Land size',
			'data' => trimTrailingZeroes(number_format((float)$object->landSize->_, 2, '.', ' ')) . ' m²',
		];
	}

	if ($object->energyClass && $object->energyClass !== 'none') {
		$details[] = [
			'label' => 'Energy mark',
			'data' => pll__($object->energyClass),
		];
	}

	if (isset($object->heatings) && !empty($object->heatings)) {
		$heatings = '';
        if (is_array($object->heatings->heating)) {
            foreach ($object->heatings->heating as $key => $heating) {
                if ($key == 0) {
                    $heatings .= NnaTranslations::getInstance()->__($heating);
                } else{
                    $heatings .= ', ' . NnaTranslations::getInstance()->__($heating);
                }
            }
        } else if (is_string($object->heatings->heating)) {
            $heatings = NnaTranslations::getInstance()->__($object->heatings->heating);
        }
		$details[] = [
			'label' => 'Heating',
			'data' => strtolower($heatings),
		];
	}

	if ($object->parking && !empty($object->parking->type)) {
		if (is_array($object->parking->type)) {
			$parking = getCommaSeparatedTranslationsFromArray($object->parking->type);
		} else if (strpos($object->parking->type, ',') !== false) {
			$parking = getCommaSeparatedTranslationsFromString($object->parking->type);
		} else {
			$parking = NnaTranslations::getInstance()->__($object->parking->type);
		}
		$details[] = [
			'label' => 'Parking',
			'data' => strtolower($parking),
		];
	}

	if ($object->sewer) {
		$details[] = [
			'label' => 'Sewer',
			'data' => strtolower(NnaTranslations::getInstance()->__($object->sewer)),
		];
	}

	if ($object->roof) {
		$details[] = [
			'label' => 'Roof',
			'data' => strtolower(NnaTranslations::getInstance()->__($object->roof)),
		];
	}

	if ($object->gasSupply) {
		$details[] = [
			'label' => 'Gas',
			'data' => strtolower(NnaTranslations::getInstance()->__($object->gasSupply)),
		];
	}

	if ($object->coldWater && !empty($object->coldWater)) {
		if (is_array($object->coldWater)) {
			$coldWater = getCommaSeparatedTranslationsFromArray($object->coldWater);
		} else {
			$coldWater = NnaTranslations::getInstance()->__($object->coldWater);
		}
		$details[] = [
			'label' => 'Water',
			'data' => strtolower(NnaTranslations::getInstance()->__($coldWater)),
		];
	}

	return $details;
}

function getCommaSeparatedTranslationsFromString($inputString) {
	return getCommaSeparatedTranslationsFromArray(explode(',', $inputString));
}

function translateArray($untranslatedArray) {
	return array_map(function($item) {
		return NnaTranslations::getInstance()->__($item);
	}, $untranslatedArray);
}

function getCommaSeparatedTranslationsFromArray($untranslatedArray) {
	return implode(', ', translateArray($untranslatedArray));
}

function my_acf_google_map_api($api) {
	$api['key'] = 'AIzaSyCar7cCzmzycqrt-AUDC59ZPE8XXIZLA7U';

	return $api;
}
add_filter('acf/fields/google_map/api', 'my_acf_google_map_api');

function consultation_form_submit()
{
    $subject = (isset($_POST['subject']) && $_POST['subject'])? $_POST['subject'] : pll__('Consultation Email Subject');

    $headers = [
        "From: Pindi Kinnisvara <<EMAIL>>",
        "Reply-To: {$_POST['customer_name']} <{$_POST['customer_email']}>",
    ];

    $message = "";
    $message .= pll__('Asset Type') . ": " . $_POST['asset_type'] . "\n";
    $message .= pll__('Asset Address') . ": " . $_POST['asset_address'] . "\n";
    $message .= pll__('Email') . ": " . $_POST['customer_email'] . "\n";
    $message .= pll__('Phone') . ": " . $_POST['customer_phone'] . "\n";
    $message .= pll__('Name') . ": " . $_POST['customer_name'] . "\n";
    $message .= pll__('Notes') . ": \n" . $_POST['customer_message'] . "\n";

	$recipient = (isset($_POST['recipient']) && $_POST['recipient'])? $_POST['recipient'] : '<EMAIL>';

    if (!wp_mail( $recipient, $subject, $message, $headers)) {
        wp_send_json([
            'status' => 'error',
            'action' => $_POST['action'],
            'data' => $_POST,
            'message' => pll__('Error has occurred! Please try again later.'),
        ]);
    }

    wp_send_json([
        'status' => 'success',
        'action' => $_POST['action'],
        'data' => $_POST,
        'message' => pll__('Message sent!'),
    ]);
}
add_action('wp_ajax_consultation_form_submit', 'consultation_form_submit');
add_action('wp_ajax_nopriv_consultation_form_submit', 'consultation_form_submit');

// Modify 'object' post type permalink structure
add_action('registered_post_type', function($post_type) {
	if ($post_type !== 'object') {
		return;
	}

	global $wp_rewrite;
	if ( ! $wp_rewrite->extra_permastructs[$post_type]) {
		return;
	}

	$struct = $wp_rewrite->extra_permastructs[$post_type]['struct'];
	$struct = '/kinnisvarapakkumised' . substr($struct, strlen('/object'));

	$wp_rewrite->extra_permastructs[$post_type]['struct'] = $struct;
});

function form_page_template_processing($type, $admin_email)
{
	if ( ! isset($_POST['email']) || ! $admin_email)
		return '';

	$failure_msg = pll__('Vormi saatmisel tekkisid tehnilised tõrked. Probleemi jätkumisel võtke ühendust saidi halduriga.');

	if ($type == 'hindamine') {
		foreach (['address','nimi', 'maakond'] as $field) {
			if ( ! isset($_POST[$field]) || strlen($_POST[$field]) > 100)
				return $failure_msg;
		}

		$valid_options = ['korter','maja','maatükk','äripind'];
		if (
			! isset($_POST['liik'])
			|| ! in_array($_POST['liik'], $valid_options)
		) {
			return $failure_msg;
		}
	}

	if (
		! isset($_POST['email'])
		|| strlen($_POST['email']) > 100
		|| (strpos($_POST['email'], '@') === false)
	) {
		return $failure_msg;
	}

	if (
		! isset($_POST['telefon'])
		|| strlen($_POST['telefon']) > 100
		|| (preg_match('/^\+?[0-9 ]+$/', $_POST['telefon']) === 0)
	) {
		return $failure_msg;
	}

	$extra_info = $_POST['lisainfo'] ?? '';
	if (strlen($extra_info) > 350) {
		return $failure_msg;
	}

	switch ($type) {
		case 'hindamine':
			$form_name = pll__('Hindamise');
			break;

		case 'vahendamine':
			$form_name = pll__('Vahendamise');
			break;

		case 'haldus':
			$form_name = pll__('Halduse');
			break;

		default:
			return $failure_msg;
	}

	$subject = $form_name . ' ' . pll__('kontaktivormi vastus');
	$message = sprintf(pll__('Täideti %s kontaktivorm'), $form_name) . "\n";
	$headers = ["From: Pindi Kinnisvara <<EMAIL>>"];

	if ($type == 'hindamine') {
		$headers[] = "Reply-To: {$_POST['nimi']} <{$_POST['email']}>";

		$message .= pll__('Vara liik') . ": " . $_POST['liik'] . "\n";
		$message .= pll__('Vara aadress') . ": " . $_POST['address'] . "\n";
		$message .= pll__('Maakond') . ": " . $_POST['maakond'] . "\n";
		$message .= pll__('Nimi') . ": " . $_POST['nimi'] . "\n";
		
	}
	$message .= pll__('E-mail') . ": " . $_POST['email'] . "\n";
	$message .= pll__('Telefon') . ": " . $_POST['telefon'] . "\n";

	if ($extra_info) {
		$message .= pll__('Lisainfo') . ": \n" . $extra_info . "\n";
	}


	if (wp_mail( $admin_email, $subject, $message, $headers)) {
		return pll__('Täname! Oleme Sinu kirja kätte saanud.');
	}

	return $failure_msg;
}

// Make mobile menu always open from 1st menu level
add_filter('wp_nav_menu_objects', function($sorted_menu_items, $args) {
	if (isset($args->theme_location) && $args->theme_location === 'mobile') {
		foreach ($sorted_menu_items as $menu_item) {
			if ($menu_item->current_item_parent || $menu_item->current_item_ancestor) {
				$menu_item->current = false;
				$menu_item->current_item_ancestor = false;
				$menu_item->current_item_parent = false;
				$key = array_search('current-menu-ancestor', $menu_item->classes);
				if ($key !== false) unset($menu_item->classes[$key]);
				$menu_item->current_item_parent = false;
				$key = array_search('current-menu-parent', $menu_item->classes);
				if ($key !== false) unset($menu_item->classes[$key]);
			}
		}
	}
	return $sorted_menu_items;
}, 10, 2);

include 'inc/shortcodes.php';
include 'inc/broker.php';
include 'inc/wpcf7.php';
include 'inc/gravityform.php';
new ws_broker_settings();
new WS_WPCF7();
new WS_Gravity_Forms();



/**
 * Enqueues the Inaadress JavaScript library for use in Contact Form 7.
 */
function enqueue_inaadress_script() {
    // Only enqueue on pages that have the InAadressDiv
    if (is_page() && has_shortcode(get_post()->post_content, 'contact-form-7')) {
        // Enqueue the InAadress library
        wp_enqueue_script(
            'inaadress',
            'https://inaadress.maaamet.ee/inaadress/js/inaadress.min.js?d=20220510',
            array('jquery'),
            '20220510',
            true
        );

        // Add inline script for initialization
        wp_add_inline_script('inaadress', '
            jQuery(document).ready(function($) {
                var inAadressContainer = document.getElementById("InAadressDiv");
                if (inAadressContainer) {
                            var inAadress = new InAadress({
                "container": "InAadressDiv",
                "mode": 3,
                "ihist": "1993",
                "appartment": 0,
                "lang": "et"
            });

                    // Wait for the result container to be created
                    var checkResults = setInterval(function() {
                        var resultContainer = document.querySelector(".inads-result-container");
                        if (resultContainer) {
                            clearInterval(checkResults);
                            
                            // Listen for clicks on result items
                            resultContainer.addEventListener("click", function(event) {
                                // Find the closest li element
                                var listItem = event.target.closest("li");
                                if (listItem) {
                                    var span = listItem.querySelector("span");
                                    if (span) {
                                        var address = span.getAttribute("title");
                                        console.log("Selected address:", address);
                                        
                                        var hiddenAddressField = document.getElementById("inaadress-selected-address");
                                        if (hiddenAddressField) {
                                            hiddenAddressField.value = address;
                                        }
                                    }
                                }
                            });

                            // Also listen for the clear button
                            var clearButton = document.querySelector(".inads-input-clear");
                            if (clearButton) {
                                clearButton.addEventListener("click", function() {
                                    console.log("Address cleared");
                                    var hiddenAddressField = document.getElementById("inaadress-selected-address");
                                    if (hiddenAddressField) {
                                        hiddenAddressField.value = "";
                                    }
                                });
                            }
                        }
                    }, 100);

                    // Clean up interval after 5 seconds if container is never found
                    setTimeout(function() {
                        clearInterval(checkResults);
                    }, 5000);
                }
            });
        ');

        // Add inline CSS to hide the copyright notice
        wp_add_inline_style('style', '.inads-copyright { display: none !important; }');
    }
}
add_action('wp_enqueue_scripts', 'enqueue_inaadress_script');