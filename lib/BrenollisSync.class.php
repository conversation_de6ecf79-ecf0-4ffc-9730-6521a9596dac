<?php

class BrenollisSync extends Sync
{

    const OPTIONS_SLUG = 'brenollisSync';
    const SKIPPED_TRANSLATION_PREFIX = 'brenollis_skipped';
    const TRANSLATION_PREFIX = 'brenollis_';

    private static $instance;
    private $_token;
    private $_host;

    function __construct()
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        $this->_token = $options['apiKey'];

        $url = parse_url($options['apiUrl']);
        $this->_host = $url['scheme'] . '://' . $url['host'] . '/api/';
    }

    public function fixMissingPosts()
    {
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');

        $count = 0;
        $params = ['Metainfo.source' => BrenollisToScoroMapper::SOURCE];
        foreach ($api->getObjects($params) as $object) {
            $postsMissing = false;
            foreach ($object->PostIds as $lang => $postId) {
                $post = get_post($postId);
                if (!$post) {
                    $postsMissing = true;
                    break;
                }
            }
            if ($postsMissing) {
                $objectId = $object->Object->originalId;
                $api->deleteObjects([
                    'Object.originalId' => $objectId,
                    'Metainfo.source' => BrenollisToScoroMapper::SOURCE,
                ], false);
                $this->syncObject($objectId);
                echo 'fixed ' . $objectId . PHP_EOL;
                $count++;
            }
        }
        echo 'fixed total: ' . $count . PHP_EOL;
    }

/*     public function findMissingPosts()
    {
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');

        $count = 0;
        $params = ['Metainfo.source' => BrenollisToScoroMapper::SOURCE];
        foreach ($api->getObjects($params) as $object) {
            echo "Posts missing count {$object->Object->originalId} ".json_encode($object->PostIds).PHP_EOL;
            //file_put_contents(NNA_PATH . '/log/debug.log',"Posts missing count {$object->Object->originalId} ".json_encode($object->PostIds).PHP_EOL, FILE_APPEND);
            $postsMissing = false;
            foreach ($object->PostIds as $lang => $postId) {
                $post = get_post($postId);
                if (!$post) {
                    $postsMissing = true;
                    break;
                }
            }
            if ($postsMissing) {
                $count++;

            }
        }

    } */

    public function deleteAll()
    {
        $now = (new DateTime())->getTimestamp();

        $api = NnaApi::getInstance();
        $condition = [
            'Metainfo.source' => BrenollisToScoroMapper::SOURCE
        ];

        $deleted = $api->deleteObjects($condition, false)->getDeletedCount();
        echo 'deleted: ' . $deleted . PHP_EOL;
        NewtimeNextApi::setOptionKey(self::OPTIONS_SLUG, 'lastSync', $now);
    }

    public function deleteObjectsByIds($ids)
    {
        $deleted = 0;
        if (!empty($ids)) {
            $api = NnaApi::getInstance();
            $condition = [
                'Object.originalId' => [
                    '$in' => $ids,
                ],
                'Metainfo.source' => BrenollisToScoroMapper::SOURCE
            ];

            $deleted = $api->deleteObjects($condition, false)->getDeletedCount();
        }
        return $deleted;
    }

    public function deleteObjectsByIdsNotIn($ids)
    {
        $deleted = 0;
        if (!empty($ids)) {
            $api = NnaApi::getInstance();
            $condition = [
                'Object.originalId' => [
                    '$nin' => $ids,
                ],
                'Metainfo.source' => BrenollisToScoroMapper::SOURCE
            ];

            $deleted = $api->deleteObjects($condition, false)->getDeletedCount();
        }
        return $deleted;
    }

    public function fakeScoroBrokerResponse($brenollisResponse)
    {
        $mapper = new BrenollisToScoroUserMapper();
        return $mapper->createResponse($brenollisResponse);
    }

    public function fakeScoroResponse($brenollisObject)
    {
        $mapper = new BrenollisToScoroObjectMapper();
        return $mapper->createResponse($brenollisObject);
    }

    public function fixObjectFields($condition = [])
    {
        $mapper = new BrenollisToScoroObjectMapper();
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');
        foreach ($collection->find($condition, array('noCursorTimeout' => true)) as $object) {
            $fixedObject = $mapper->fixObject($object->Object);
            $collection->updateOne(
                array('_id' => $object->_id),
                array(
                    '$set' => array(
                        'Object' => $fixedObject,
                    )
                )
            );
        }
    }

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getActiveIds()
    {
        $ids = [];
        $page = 0;
        do {
            $queryResponse = $this->getAdverts(['size' => 500, 'page' => $page]); // Increased from 150 to 500
            $filtered = array_filter($queryResponse->content, function ($item) {
                return $item->status == 'active';
            });
            $ids = array_merge($ids, array_map(function ($item) {
                return $item->id;
            }, $filtered));
            $page++;
        } while ($queryResponse->totalPages >= $page);
        return $ids;
    }

    public function getAdvert($id, $parameters = [])
    {
        $url = new Url('adverts/' . $id, $parameters);
        return $this->_sendRequest($url);
    }

    public function getAdverts($parameters = [])
    {
        $url = new Url('adverts', $parameters);
        return $this->_sendRequest($url);
    }

    /**
     * @param array $parameters
     * @return mixed
     * @throws ApiException
     */
    public function reportAdverts($parameters = [])
    {
        $url = new Url('adverts/report', $parameters);
        $url->method = 'POST';
        $url->postFields = $parameters;
        return $this->_sendRequest($url);
    }

    public function getAllMissingIds()
    {
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');


        $activeIds = $this->getActiveIds();
        //var_dump(count(array_unique($activeIds)));
        $forSync = [];
        $count = 0;
        foreach ($activeIds as $id) {
            $oldObject = $api->getObject([
                'Object.originalId' => $id,
                'Metainfo.source' => BrenollisToScoroMapper::SOURCE
            ]);
            if (!$oldObject) {
                $forSync[] = $id;
            } else {
                $count++;
            }
        }
        return $forSync;
    }

    public function getManagers($id, $parameters = [])
    {
        $url = new Url('adverts/' . $id . '/managers', $parameters);
        return $this->_sendRequest($url);
    }

    public function getSkippableTranslations($asKeys = true)
    {
        $skippableTranslations = [
            'CUSTOM_FIELD_OBJECT_COLD_WATER_CENTRAL',
            'CUSTOM_FIELD_OBJECT_ALARM_INSTALLED',
            'CUSTOM_FIELD_OBJECT_ALARM_MISSING',
            'CUSTOM_FIELD_OBJECT_ALARM_CABLING',
        ];
        if ($asKeys) {
            return array_flip($skippableTranslations);
        }
        return $skippableTranslations;
    }

    public function getTranslations($parameters = [])
    {
        $url = new Url('domain-values', $parameters);
        return $this->_sendRequest($url);
    }

    public function getTranslationsByDomain($parameters = [])
    {
        $translations = $this->getTranslations($parameters);
        $domains = [];
        foreach ($translations as $translation) {
            if (!isset($domains[$translation->domain])) {
                $domains[$translation->domain] = [];
            }
            $domains[$translation->domain][] = $translation->code . ' - ' . $translation->translations->et;
        }
        return $domains;
    }

    public function getUser($id, $parameters = [])
    {
        $url = new Url('users/' . $id . '/profile', $parameters);
        return $this->_sendRequest($url);
    }

    /**
     * @param $objects
     * @return array
     * @throws Exception
     */
    public function insertObjects($objects)
    {
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');
        $newObjectsPosts = [];
        foreach ($objects as $object) {
            $newObjectsPosts[] = $this->insertObject($object, $collection);
        }

        return $newObjectsPosts;
    }

    public function registerTranslations($translations)
    {
        $translator = NnaTranslations::getInstance();

        // $skipTranslations = $this->getSkippableTranslations();
        foreach ($translations as $code => $translation) {
            $prefix = self::TRANSLATION_PREFIX;
            $translator->checkTranslation($translation->code, $prefix);
        }
    }

    public function syncUsers()
    {
    }

    public function syncTranslations()
    {
        $translations = $this->getTranslations();
        $this->registerTranslations($translations);
        $this->translateStrings($translations);
    }

    public function syncAll()
    {
        ini_set("memory_limit", "1024M"); // Increased from 512M to 1024M
        echo 'sync all';
        $response = [];
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');

        $ids = $this->getActiveIds();
        $response['deleted'] = $this->deleteObjectsByIdsNotIn($ids);

        $page = 0;
        $count = 0;
        $response['inserted'] = 0;
        do {
            $info = $this->updatePage($page, 500); // Increased from 100 to 500
            $count += $info['updated'];
            $response['inserted'] += $info['inserted'];
            echo 'updated: ' . $count . ' inserted: ' . $response['inserted'] . ' page: ' . $page . PHP_EOL;
            gc_collect_cycles();

            $page++;
        } while ($info['totalPages'] >= $page);
        $response['updated'] = $count;

        print_r($response);
        echo json_encode($response);
    }

    public function fixObjectBrokerPostIds() {
        $WP_Brokers = get_posts([
            "post_type" => "broker",
            "posts_per_page" => -1,
        ]);

        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.brokers');

        $updated = [];
        foreach($WP_Brokers as $WP_Broker) {
            $mongoBrokers = $collection->findOne(array('Broker.name' => $WP_Broker->post_title));
            if(!$mongoBrokers) {
                continue;
            }

            $matchPostIds = true;
            $wpBrokerExists = false;
            foreach((array)$mongoBrokers->PostIds as $language => $postId) {
                if($postId === pll_get_post($WP_Broker->ID, $language)) {
                    continue;
                }
                $matchPostIds = false;

                foreach($WP_Brokers as $nested) {
                    if($nested->ID === $postId) {
                        $wpBrokerExists = true;
                    }
                }
            }

            if($matchPostIds || $wpBrokerExists) {
                continue;
            }

            $newPostIds = [];
            foreach(['et', 'ru', 'en'] as $language) {
                $newPostIds[$language] = pll_get_post($WP_Broker->ID, $language);
            }
            $updated["{$WP_Broker->ID}_{$WP_Broker->post_title}"] = $newPostIds;
            $mongoBrokers = $collection->updateOne(['Broker.name' => $WP_Broker->post_title], array(
                '$set' => array(
                    'PostIds' => $newPostIds,
                )
            ));
            file_put_contents(NNA_PATH . '/log/synclog.log', "Fixing broker: {$updated}".PHP_EOL, FILE_APPEND);
        }
    }

    public function updatePage($page = 0, $size = 150, $daysDiff = null)
    {
        $response = [];
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');

        $params = ['size' => $size, 'page' => $page, 'sortField' => 'id', 'sortDirection' => 'ASC'];
        //if ($daysDiff) {
            $params['daysDiff'] = 16;
        //}

        $queryResponse = $this->getAdverts($params);
        $forInsert = [];
        $count = 0;
        $ids = [];
        $reports = [];

        foreach ($queryResponse->content as $object) {
            if ($object->status == 'active') {
                $ids[] = $object->id;
                $oldObject = $api->getObject([
                    'Object.originalId' => $object->id,
                    'Metainfo.source' => BrenollisToScoroMapper::SOURCE
                ]);
                $brenollisObject = $this->getAdvert($object->id);
                $fakeResponse = $this->fakeScoroResponse($brenollisObject);
                if ($oldObject) {
                    $this->updateObject($fakeResponse, $oldObject, $collection);

                    $postId = $this->getPostId($oldObject);
                    if ($postId) {
                        $reports[] = $this->getAdvertReport($object->id, $postId, 'Updated');
                    }

                    $count++;
                } else {
                    $forInsert[] = $fakeResponse;
                }
            }
        }

        $newObjectsPosts = $this->insertObjects($forInsert);
        foreach ($newObjectsPosts as $newObjectPost) {
            if ($newObjectPost && isset($newObjectPost['ids']) && isset($newObjectPost['ids']['et'])) {
                $reports[] = $this->getAdvertReport($newObjectPost['objectId'], $newObjectPost['ids']['et'], 'Updated');
            }
        }

       try {
           $reportsResponse = $this->reportAdverts(['reports' => $reports]);
           file_put_contents(NNA_PATH . '/log/reports-log.log', date('Y-m-d H:i:s') . " - Starting advert reports request...".PHP_EOL, FILE_APPEND);
           file_put_contents(NNA_PATH . '/log/reports-log.log', json_encode($reports).PHP_EOL, FILE_APPEND);
           file_put_contents(NNA_PATH . '/log/reports-log.log', "Response: ".PHP_EOL, FILE_APPEND);
           file_put_contents(NNA_PATH . '/log/reports-log.log', json_encode($reportsResponse).PHP_EOL, FILE_APPEND);
       } catch (ApiException $e) {
           file_put_contents(NNA_PATH . '/log/reports-log.log', $e->getMessage().PHP_EOL, FILE_APPEND);
       }
        print_r($reportsResponse);

        $response['inserted'] = count($forInsert);
        $response['updated'] = $count;
        $response['ids'] = $ids;
        $response['totalPages'] = $queryResponse->totalPages;
        gc_collect_cycles();
        return $response;
    }


    /**
     * @param int $id
     * @param int $externalId
     * @param string $message
     * @param string $status
     * @return array
     */
    private function getAdvertReport($id, $externalId, $message = 'Imported', $status = 'success')
    {
        return [
            'id' => $id,
            'externalId' => $externalId,
            'message' => $message,
            'status' => $status,
        ];
    }

    public function startObjectSync($all = false)
    {
        // Performance optimizations
        ini_set("memory_limit", "1024M");
        ini_set("max_execution_time", 0); // No time limit

        file_put_contents(NNA_PATH . '/log/synclog.log', 'BrenollisSync::startObjectSync called with $all=' . ($all ? 'true' : 'false') . ' at ' . date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        if ($all) {
            return $this->syncAll();
        }
        $response = [];
        $response['inserted'] = 0;

        $ids = $this->getActiveIds();
        $response['deleted'] = $this->deleteObjectsByIdsNotIn($ids);

        $lastSync = NewtimeNextApi::getOption(self::OPTIONS_SLUG)['lastSync'];
        $now = (new DateTime())->getTimestamp();
        $response['dev'] = ['lastSync' => $lastSync];
        $dayDiff = floor(($now - $lastSync) / (24 * 60 * 60)) + 1;
        $response['dev']['diff'] = $now - $lastSync;
        $response['dev']['dayDiff'] = $dayDiff;

        $page = 0;
        $count = 0;
        do {
            $info = $this->updatePage($page, 500, $dayDiff); // Increased from 100 to 500
            $count += $info['updated'];
            $response['inserted'] += $info['inserted'];
            echo 'updated: ' . $count . ' inserted: ' . $response['inserted'] . ' page: ' . $page . PHP_EOL;
            gc_collect_cycles();
            $page++;
        } while ($info['totalPages'] >= $page);
        $response['updated'] = $count;

        NewtimeNextApi::setOptionKey(self::OPTIONS_SLUG, 'lastSync', $now);
        print_r($response);
        echo json_encode($response);
    }

    /**
     * Fast sync mode - optimized for speed
     * Uses larger page sizes and reduced logging
     */
    public function fastSync()
    {
        ini_set("memory_limit", "2048M"); // Even more memory
        ini_set("max_execution_time", 0);

        file_put_contents(NNA_PATH . '/log/synclog.log', 'BrenollisSync::fastSync started at ' . date('Y-m-d H:i:s') . PHP_EOL, FILE_APPEND);

        $response = [];
        $response['inserted'] = 0;

        $ids = $this->getActiveIds();
        $response['deleted'] = $this->deleteObjectsByIdsNotIn($ids);

        $lastSync = NewtimeNextApi::getOption(self::OPTIONS_SLUG)['lastSync'];
        $now = (new DateTime())->getTimestamp();
        $dayDiff = floor(($now - $lastSync) / (24 * 60 * 60)) + 1;

        $page = 0;
        $count = 0;
        do {
            $info = $this->updatePage($page, 1000, $dayDiff); // Even larger page size
            $count += $info['updated'];
            $response['inserted'] += $info['inserted'];

            // Less frequent logging
            if ($page % 5 == 0) {
                echo 'Fast sync - page: ' . $page . ' updated: ' . $count . ' inserted: ' . $response['inserted'] . PHP_EOL;
            }

            // Less frequent garbage collection
            if ($page % 10 == 0) {
                gc_collect_cycles();
            }

            $page++;
        } while ($info['totalPages'] >= $page);

        $response['updated'] = $count;
        NewtimeNextApi::setOptionKey(self::OPTIONS_SLUG, 'lastSync', $now);

        file_put_contents(NNA_PATH . '/log/synclog.log', 'BrenollisSync::fastSync completed at ' . date('Y-m-d H:i:s') . ' - updated: ' . $count . ' inserted: ' . $response['inserted'] . PHP_EOL, FILE_APPEND);

        print_r($response);
        echo json_encode($response);
        return $response;
    }

    public function syncObject($objectId)
    {
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.objects');

        $brenollisObject = $this->getAdvert($objectId);
        if ($brenollisObject->status == 'active') {
            $oldObject = $api->getObject([
                'Object.originalId' => $objectId,
                'Metainfo.source' => BrenollisToScoroMapper::SOURCE
            ]);

            $fakeResponse = $this->fakeScoroResponse($brenollisObject);
            if ($oldObject) {
                $this->updateObject($fakeResponse, $oldObject, $collection);
            } else {
                $this->insertObject($fakeResponse, $collection);
            }
        }
    }

    public function translateLanguage($language, $translations)
    {
        $mo = new PLL_MO();
        $mo->import_from_db($language);

        // $skipTranslations = $this->getSkippableTranslations();
        foreach ($translations as $code => $translation) {
            // if (isset($translation->translations->{$language->slug}) && !isset($skipTranslations[$code])) {
            $mo->add_entry($mo->make_entry($translation->code, $translation->translations->{$language->slug}));
            // }
        }
        $mo->export_to_db($language);
    }

    public function translateStrings($translations)
    {
        global $polylang;
        $defaultLanguage = pll_default_language();
        foreach ($polylang->model->get_languages_list() as $language) {
            $this->translateLanguage($language, $translations);
        }
    }

    /**
     * @param Url $url
     * @return mixed
     * @throws ApiException|Exception
     */
    private function _sendRequest(Url $url)
    {
        if (!$this->_host or !$this->_token) {
            throw new \Exception('Missing parameters');
        }

        $fullUrl = $this->_host . $url->getUrlWithArgs();

        $handle = curl_init($fullUrl);

        curl_setopt_array($handle, $url->getCurlParameters());

        curl_setopt($handle, CURLOPT_HTTPHEADER,
            array(
                'X-API-KEY: ' . $this->_token,
                'Content-Type: application/json',
            )
        );
        curl_setopt($handle, CURLOPT_HEADER, 0);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);

        //run
        $response = curl_exec($handle);
        $error = curl_error($handle);
        $errorNumber = curl_errno($handle);

        $errorcode = curl_getinfo($handle, CURLINFO_HTTP_CODE);
        curl_close($handle);
        if ($errorcode < 200 || $errorcode >= 300) {
            throw new ApiException($response, $errorcode);
        }

        if ($error) {
            throw new ApiException('CURL error: ' . $response . ':' . $error . ': ' . $errorNumber);
        }

        return json_decode($response);
    }

    /**
     * @param object|null $oldObject
     * @return int|null
     */
    private function getPostId($oldObject)
    {
        if ($oldObject->PostIds->et) {
            return $oldObject->PostIds->et;
        }

        return null;
    }
}

class BrenollisToScoroMapper
{
    const SOURCE = 'Brenollis';

    public static $countryMap = [
        'est' => 'Eesti',
    ];

    public function createHideableString($value, $hidden = false)
    {
        $hideableStringType = new HideableStringType();
        $hideableStringType->_ = $value;
        $hideableStringType->hidden = $hidden;
        return $hideableStringType;
    }

    public function createMetainfo()
    {
        $metainfo = new Metainfo();
        $metainfo->source = self::SOURCE;
        return $metainfo;
    }

    public function findMatchingBrokerId($object)
    {
        $profiles = $this->getBrokerProfiles($object->id);

        return $this->findBrokerFromProfiles($profiles);
    }

    public function findBrokerFromProfiles($profiles)
    {
        foreach ($profiles as $profile) {
            if ($profile->user->email) {
                $condition = ['Broker.email' => $profile->user->email];
                $results = NnaApi::getInstance()->getBrokers($condition);
                if (!empty($results)) {
                    return $results[0]->Broker->originalId;
                }
            }
        }

        return null;
    }

    public function getBrokerProfiles($objectId)
    {
        $api = BrenollisSync::getInstance();
        return $api->getManagers($objectId);
    }
}

class BrenollisToScoroUserMapper extends BrenollisToScoroMapper
{
    public function createResponse($broker)
    {
        $result = new getBrokerByIdResponse();
        $result->Broker = $this->mapBroker($broker);
        $result->Metainfo = $this->createMetainfo();

        return $result;
    }

    public function findOrCreateMatchingBrokerId($object)
    {
        $profiles = $this->getBrokerProfiles($object->id);

        if (!count($profiles)) {
            return null;
        }

        $id = $this->findBrokerFromProfiles($profiles);
        if ($id) {
            return $id;
        }

        return $this->saveBroker($profiles[0]);
    }

    public function saveBroker($profile)
    {
        $scoroBroker = $this->createResponse($profile);

        $wpHandler = WpHandler::getInstance();
        $posts = $wpHandler->getBrokersByEmail($profile->user->email);
        $postIds = [];
        foreach ($posts as $post) {
            $lang = pll_get_post_language($post->ID);
            $postIds[$lang] = $post->ID;
        }

        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName() . '.brokers');
        $collection->insertOne(array(
            'PostIds' => $postIds,
            'Broker' => $scoroBroker->Broker,
            'Metainfo' => $scoroBroker->Metainfo,
        ));

        $savedObject = $collection->findOne(['Broker.originalId' => $scoroBroker->Broker->originalId]);
        WpHandler::getInstance()->updateBrokerPosts($savedObject, $scoroBroker);

        return $scoroBroker->Broker->originalId;
    }

    public function getCompanyId($brenollisResponse)
    {
        if (isset($brenollisResponse->organization) && isset($brenollisResponse->organization->id)) {
            return $brenollisResponse->organization->id;
        }
        return null;
    }

    public function getPictureUrl($brenollisResponse)
    {
        if (isset($brenollisResponse->image) && isset($brenollisResponse->image->small)) {
            return $brenollisResponse->image->small;
        }
        return null;
    }

    public function mapBroker($brenollisResponse)
    {
        $scoroBroker = new Broker();
        // hack to make sure brenollis ids don't overlap old pindi originalIds
        $scoroBroker->originalId = 100000 + $brenollisResponse->user->id;
        $scoroBroker->companyId = $this->getCompanyId($brenollisResponse);
        $scoroBroker->status = 'active';
        $scoroBroker->licenceNumber = '';
        $scoroBroker->name = $brenollisResponse->user->fullName;
        $scoroBroker->phone = $brenollisResponse->user->phoneNumber;
        $scoroBroker->mobile = $brenollisResponse->user->phoneNumber;
        $scoroBroker->email = $brenollisResponse->user->email;
        $scoroBroker->pictureUrl = $this->getPictureUrl($brenollisResponse);
        $scoroBroker->fax = '';
        $scoroBroker->skype = '';
        $scoroBroker->msn = '';

        return $scoroBroker;
    }
}

class BrenollisToScoroObjectMapper extends BrenollisToScoroMapper
{
    public function createAreaSizeType($value, $unit = 'm2')
    {
        $areaSizeType = new AreaSizeType();
        $areaSizeType->_ = $value;
        $areaSizeType->unit = $unit;
        return $areaSizeType;
    }

    public function createResponse($object)
    {
        $result = new getObjectByIdResponse();
        $result->Object = $this->mapObject($object);
        $result->Metainfo = $this->createMetainfo();

        return $result;
    }


    public function fixObject($object)
    {
        $api = BrenollisSync::getInstance();
        $object->level1 = $api->getCountyNameMapper()->mapName($object->level1);
        $object->level2 = $api->getCityNameMapper()->mapName($object->level2);

        $objectTypeMap = $this->getObjectTypeMap();
        $objectType = $object->objectTypes->type;
        $object->objectTypes->type = isset($objectTypeMap[$objectType]) ? $objectTypeMap[$objectType] : $objectType;

        $transactionTypeMap = $this->getTypesMap();
        $transactionType = $object->transactions->transaction->type;
        if (isset($transactionTypeMap[$transactionType])) {
            $object->transactions->transaction->type = $transactionTypeMap[$transactionType];
        }

        return $object;
    }

    public function getObjectTypeMap()
    {
        return [
            'OBJECT_TYPE_KASI_WAREHOUSE_MANUFACTURING' => 'OBJECT_TYPE_KOM_WAREHOUSE_MANUFACTURING',
            'OBJECT_TYPE_KASI_TRANSPORT' => 'OBJECT_TYPE_KOM_TRANSPORT',
            'OBJECT_TYPE_KASI_OFFICE' => 'OBJECT_TYPE_KOM_OFFICE',
            'OBJECT_TYPE_KASI_CATERING' => 'OBJECT_TYPE_KOM_CATERING',
            'OBJECT_TYPE_KASI_GARAGE' => 'OBJECT_TYPE_KOM_GARAGE',
            'OBJECT_TYPE_KASI_FARM_BUILDING' => 'OBJECT_TYPE_KOM_FARM_BUILDING',
            'OBJECT_TYPE_KASI_OTHER' => 'OBJECT_TYPE_KOM_OTHER mitteeluruum',
            'OBJECT_TYPE_KASI_SERVICES' => 'OBJECT_TYPE_KOM_SERVICES',
            'OBJECT_TYPE_KASI_STORE' => 'OBJECT_TYPE_KOM_STORE',
            'OBJECT_TYPE_KASI_ACCOMMODATION' => 'OBJECT_TYPE_KOM_ACCOMMODATION',
        ];
    }

    public function getSanitaryState()
    {
        return [
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_ROOFLESS_BOX' => 'inCompletionStage',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_CLOSED_BOX' => 'inCompletionStage',
            'CUSTOM_FIELD_OBJECT_SANITARY_FURNITURE_STATE_ROOF_BOX' => 'inCompletionStage',
        ];
    }

    public function getTypesMap()
    {
        return ['rental' => 'rent'];
    }

    public function mapBrokers($object)
    {
        $mapper = new BrenollisToScoroUserMapper();

        $broker = new BrokersType();
        $broker->originalId = $mapper->findOrCreateMatchingBrokerId($object);
        return $broker;
    }

    public function mapCadastralNumber($object)
    {
        $cadastralNumbersType = new CadastralNumbersType();
        $cadastralNumbersType->cadastralNumber = $object->cadasterNumber;
        return $cadastralNumbersType;
    }

    public function mapCountry($object)
    {
        if (isset(self::$countryMap[$object->address->country])) {
            return self::$countryMap[$object->address->country];
        }
        return $object->address->country;
    }

    public function mapHeating($object)
    {
        $heatings = new heatings();
        $heatings->heating = explode(',', $object->heatingSystem);
        return $heatings;
    }

    public function mapInfo($object)
    {
        $info = [];
        foreach (NnaTranslations::$languageCodes as $lang => $scoroLang) {
            $langInfo = new InfoType();
            $langInfo->_ = $object->description->{$lang};
            $langInfo->language = $scoroLang;
            $info[] = $langInfo;
        }
        return $info;
    }

    public function mapObject($object)
    {
        $scoroObject = new Object_t();
        $scoroObject->objectTypes = $this->mapObjectType($object);
        $scoroObject->originalId = $object->id;
        $scoroObject->Brokers = $this->mapBrokers($object);
        $scoroObject->lastModified = $object->dateModified;
        $scoroObject->status = $object->status;
        // $scoroObject->statusDescriptor = '';
        // $scoroObject->visible = '';
        // $scoroObject->advance = PriceType;
        $scoroObject->transactions = $this->mapTransactions($object);
        $scoroObject->visible = 'all';
        $scoroObject->isBooked = $object->booked;
        $scoroObject->bookedUntil = $object->bookedUntil;

        $scoroObject->Country = $this->mapCountry($object);

        $api = BrenollisSync::getInstance();
        $scoroObject->level1 = $api->getCountyNameMapper()->mapName($object->address->county);
        $scoroObject->level2 = $api->getCityNameMapper()->mapName($object->address->city);

        $scoroObject->level3 = $object->address->locality;
        // $scoroObject->level4 = string ;
        // $scoroObject->level5 = string ;
        // $scoroObject->level6 = string ;
        // $scoroObject->postalCode = string;
        $scoroObject->street = $this->createHideableString($object->address->street);
        $scoroObject->houseNumber = $this->createHideableString($object->address->house);
        $scoroObject->apartmentNumber = $this->createHideableString($object->address->room);
        // $scoroObject->geoCoordinates = string;
        // $scoroObject->priority = '';
        $scoroObject->numberOfFloors = $object->floors;
        $scoroObject->floorNumber = $object->floor;
        $scoroObject->buildYear = $object->buildYear;
        $scoroObject->pictures = $this->mapPictures($object);
        // $scoroObject->maps = MapsType;
        // $scoroObject->virtualTours = VirtualToursType;
        // $scoroObject->videos = VideosType;
        // $scoroObject->plans = PlansType;
        // $scoroObject->attachedFiles = AttachedFilesType;
        // $scoroObject->distances = DistancesType;
        // $scoroObject->childObjects = ChildObjectsType;
        // $scoroObject->homepageUrl = anyURI;
        $scoroObject->alacrity = $this->mapSanitaryOptions($object); // condition
        $scoroObject->areaSize = $this->createAreaSizeType($object->closedNetArea);
        $scoroObject->landSize = $this->createAreaSizeType($object->propertyArea);
        $scoroObject->propertyType = $object->ownership;
        // $scoroObject->limitations = ;
        // $scoroObject->numberOfPhones = integer;
        $scoroObject->roof = $object->roof;
        // $scoroObject->realEstateNumber = ;
        $scoroObject->cadastralNumbers = $this->mapCadastralNumber($object);
        $scoroObject->parking = $this->mapParking($object);
        // $scoroObject->kitchenAreaSize = AreaSizeType;
        $scoroObject->numberOfRooms = $object->rooms;
        $scoroObject->numberOfBedrooms = $object->bedroomCount;
        // $scoroObject->numberOfBathrooms = integer;
        // $scoroObject->numberOfWcs = integer;
        // $scoroObject->numberOfLivingrooms = integer;
        // $scoroObject->numberOfKitchens = integer;
        $scoroObject->buildingStructure = $object->supportingStructure;
        $scoroObject->stove = $object->stove;
        // $scoroObject->balconies = '';
        $scoroObject->sewer = $object->sewerage;
        // $scoroObject->doors = '';
        // $scoroObject->windows = '';
        $scoroObject->heatings = $this->mapHeating($object);
        // $scoroObject->roads = RoadsType;
        // $scoroObject->renovations = renovations;
        // $scoroObject->neighbours = neighbours;
        $scoroObject->options = $this->mapOptions($object);
        $scoroObject->title = $object->salesSentence;
        $scoroObject->brokerName = $object->salesSentence;
        $scoroObject->energyClass = $object->energyLabel;
        $scoroObject->gasSupply = $object->gasSupply;
        $scoroObject->coldWater = $object->coldWater;

        return $scoroObject;
    }

    public function mapObjectType($object)
    {
        $typeMap = $this->getObjectTypeMap();
        $type = new ObjectTypesType();
        $type->type = isset($typeMap[$object->objectType]) ? $typeMap[$object->objectType] : $object->objectType;
        return $type;
    }

    public function mapOptions($object)
    {
        $baseOptions = array_values(array_filter(array_merge(
            [],
            explode(',', $object->additionalInfo),
            // explode(',', $object->coldWater),
            explode(',', $object->communications),
            // explode(',', $object->gasSupply),
            explode(',', $object->roof),
            explode(',', $object->sanitary),
            // explode(',', $object->security),
            // explode(',', $object->sewerage),
            explode(',', $object->ventilation),
            explode(',', $object->alarm)
        )));

        $options = [];
        foreach ($baseOptions as $option) {
            $optionObject = new option();
            $optionObject->_ = $option;
            $options[] = $optionObject;
        }

        $optionsObject = new options();
        $optionsObject->option = $options;
        return $optionsObject;
    }

    public function mapParking($object)
    {
        $parkingType = new ParkingType();
        $parkingType->type = $object->parking;
        return $parkingType;
    }

    public function mapPictures($object)
    {
        $pictures = [];
        foreach ($object->images as $image) {
            $picture = new MediaUrlType();
            $picture->_ = $image->large;
            $picture->hash = $image->imageId;
            $pictures[] = $picture;
        }

        $picturesType = new PicturesType();
        $picturesType->pictureUrl = $pictures;
        return $picturesType;
    }

    public function mapPrice($object)
    {
        $price = new PriceType();
        $price->_ = $object->price;
        $price->currency = 'EUR';
        $price->public = true;
        return $price;
    }

    public function mapPriceM2($object)
    {
        $price = new PriceType();
        if ($object->closedNetArea > 0) {
            $price->_ = $object->price / $object->closedNetArea;
        } else if ($object->propertyArea > 0) {
            $price->_ = $object->price / $object->propertyArea;
        }
        $price->currency = 'EUR';
        $price->public = true;
        return $price;
    }

    public function mapSanitaryOptions($object)
    {
        $sanitaryOptions = $this->getSanitaryState();
        if (isset($sanitaryOptions[$object->sanitaryFurnitureState])) {
            return $sanitaryOptions[$object->sanitaryFurnitureState];
        }
        return $object->sanitaryFurnitureState;
    }

    public function mapTransactions($object)
    {
        $transaction = new TransactionType();
        $transaction->type = $this->mapTransactionType($object);
        $transaction->price = $this->mapPrice($object);
        // $transaction->bargainPrice = ;
        $transaction->info = $this->mapInfo($object);
        // $transaction->technicalInfo = InfoType;
        // $transaction->viewedTimes = integer;
        // $transaction->expiredDate = date;
        // $transaction->externalId = integer;
        // $transaction->externalUrl = anyURI;
        $transaction->pricem2 = $this->mapPriceM2($object);

        $transactions = new TransactionsType();
        $transactions->transaction = $transaction;
        return $transactions;
    }

    public function mapTransactionType($object)
    {
        $typesMap = $this->getTypesMap();
        if (isset($typesMap[$object->type])) {
            return $typesMap[$object->type];
        }
        return $object->type;
    }

}

