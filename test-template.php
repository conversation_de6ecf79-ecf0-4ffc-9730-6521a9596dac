<?php
/**
 * Test template loading
 */

// Set up environment
$_SERVER['HTTP_HOST'] = 'pindiline.site.test';
$_SERVER['REQUEST_URI'] = '/kinnisvara-pakkumised';
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTPS'] = 'on';
$_SERVER['SERVER_PORT'] = '443';

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "Loading WordPress...\n";
require_once '/Users/<USER>/server/pindiline/wp-load.php';

echo "WordPress loaded successfully\n";

// Start session if not started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "Session started\n";

// Test if we can access the template
$template_path = get_template_directory() . '/templates/nna-objects.php';
echo "Template path: $template_path\n";

if (file_exists($template_path)) {
    echo "Template file exists\n";
    
    // Try to capture any output or errors
    ob_start();
    try {
        // Set up some basic variables that the template expects
        global $post;
        $post = get_post(17);
        setup_postdata($post);
        
        echo "Including template...\n";
        include $template_path;
        
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "Fatal Error: " . $e->getMessage() . "\n";
    }
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Template output length: " . strlen($output) . " characters\n";
    if (strlen($output) > 0) {
        echo "First 200 characters of output:\n";
        echo substr($output, 0, 200) . "\n";
    } else {
        echo "No output generated!\n";
    }
    
} else {
    echo "Template file does not exist!\n";
}

echo "Test complete\n";
